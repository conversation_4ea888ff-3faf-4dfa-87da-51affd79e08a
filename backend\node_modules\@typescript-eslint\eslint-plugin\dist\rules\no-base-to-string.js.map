{"version": 3, "file": "no-base-to-string.js", "sourceRoot": "", "sources": ["../../src/rules/no-base-to-string.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,wEAAsE;AACtE,+CAAiC;AAEjC,kCAAqE;AAErE,IAAK,UAIJ;AAJD,WAAK,UAAU;IACb,+BAAiB,CAAA;IACjB,4BAAc,CAAA;IACd,+BAAiB,CAAA;AACnB,CAAC,EAJI,UAAU,KAAV,UAAU,QAId;AASD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,sGAAsG;YACxG,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,YAAY,EACV,0EAA0E;SAC7E;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,iBAAiB,CAAC;SAChE;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAEvD,SAAS,eAAe,CAAC,IAAyB,EAAE,IAAc;YAChE,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;gBACzC,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,wBAAwB,CACxC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CACzC,CAAC;YACF,IAAI,SAAS,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE;oBACJ,SAAS;oBACT,IAAI,EAAE,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;iBAC3C;gBACD,SAAS,EAAE,cAAc;gBACzB,IAAI;aACL,CAAC,CAAC;QACL,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAa;YAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC7D,MAAM,YAAY,GAAG,QAAQ,EAAE,eAAe,EAAE,CAAC;YACjD,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5D,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,mFAAmF;YACnF,IACE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;gBACjC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,EACxC,CAAC;gBACD,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAA,kBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC1D,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,IACE,YAAY,CAAC,KAAK,CAChB,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CACb,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CACtE,EACD,CAAC;gBACD,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC1B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACjC,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;oBAE5D,IAAI,iBAAiB,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;wBAC5C,OAAO,UAAU,CAAC,MAAM,CAAC;oBAC3B,CAAC;gBACH,CAAC;gBAED,OAAO,UAAU,CAAC,KAAK,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,OAAO,UAAU,CAAC,KAAK,CAAC;YAC1B,CAAC;YAED,IAAI,iBAAiB,GAAG,IAAI,CAAC;YAC7B,IAAI,iBAAiB,GAAG,KAAK,CAAC;YAE9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACjC,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;gBAE5D,IAAI,iBAAiB,KAAK,UAAU,CAAC,MAAM,IAAI,iBAAiB,EAAE,CAAC;oBACjE,iBAAiB,GAAG,KAAK,CAAC;gBAC5B,CAAC;gBAED,IAAI,iBAAiB,KAAK,UAAU,CAAC,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACjE,iBAAiB,GAAG,IAAI,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,IAAI,iBAAiB,IAAI,iBAAiB,EAAE,CAAC;gBAC3C,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,UAAU,CAAC,SAAS,CAAC;YAC9B,CAAC;YAED,OAAO,UAAU,CAAC,KAAK,CAAC;QAC1B,CAAC;QAED,OAAO;YACL,yEAAyE,CACvE,IAA+D;gBAE/D,MAAM,QAAQ,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEzD,IAAI,IAAA,kBAAW,EAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,QAAQ,EAAE,CAAC;oBAChD,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACzC,CAAC;qBAAM,IACL,IAAA,kBAAW,EAAC,OAAO,EAAE,SAAS,CAAC,KAAK,QAAQ;oBAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACnD,CAAC;oBACD,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YACD,mFAAmF,CACjF,IAAyB;gBAEzB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAmC,CAAC;gBAC5D,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YACD,eAAe,CAAC,IAA8B;gBAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE,CAAC;oBACjE,OAAO;gBACT,CAAC;gBACD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC1C,eAAe,CAAC,UAAU,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}