{"version": 3, "file": "indent.js", "sourceRoot": "", "sources": ["../../src/rules/indent.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;AACH,iGAAiG;;AAGjG,oDAA0D;AAC1D,wEAAsE;AAMtE,kCAAqC;AACrC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,QAAQ,CAAC,CAAC;AAK7C,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC;IAC1B,qDAAqD;IACrD,sBAAc,CAAC,kBAAkB;IAEjC,cAAc;IACd,sBAAc,CAAC,iBAAiB;IAChC,sBAAc,CAAC,YAAY;IAC3B,sBAAc,CAAC,gBAAgB;IAC/B,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,eAAe;IAC9B,sBAAc,CAAC,eAAe;IAC9B,sBAAc,CAAC,eAAe;IAC9B,sBAAc,CAAC,kBAAkB;IACjC,sBAAc,CAAC,gBAAgB;IAC/B,sBAAc,CAAC,aAAa;IAC5B,sBAAc,CAAC,aAAa;IAE5B,uCAAuC;IACvC,sBAAc,CAAC,4BAA4B;IAC3C,sBAAc,CAAC,0BAA0B;IACzC,sBAAc,CAAC,WAAW;IAC1B,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,0BAA0B;IACzC,sBAAc,CAAC,iBAAiB;IAChC,sBAAc,CAAC,iBAAiB;IAChC,sBAAc,CAAC,+BAA+B;IAC9C,sBAAc,CAAC,iBAAiB;IAChC,sBAAc,CAAC,6BAA6B;IAC5C,sBAAc,CAAC,iBAAiB;IAChC,sBAAc,CAAC,YAAY;IAC3B,sBAAc,CAAC,kBAAkB;IACjC,sBAAc,CAAC,yBAAyB;IACxC,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,YAAY;IAC3B,sBAAc,CAAC,mBAAmB;IAClC,sBAAc,CAAC,gBAAgB;IAC/B,sBAAc,CAAC,WAAW;IAC1B,sBAAc,CAAC,eAAe;IAC9B,sBAAc,CAAC,sBAAsB;IACrC,sBAAc,CAAC,mBAAmB;IAClC,sBAAc,CAAC,kBAAkB;IACjC,sBAAc,CAAC,yBAAyB;IACxC,sBAAc,CAAC,aAAa;IAC5B,sBAAc,CAAC,YAAY;IAC3B,sBAAc,CAAC,iBAAiB;IAChC,cAAc;IACd,sBAAc,CAAC,aAAa;IAC5B,sBAAc,CAAC,mBAAmB;IAClC,sBAAc,CAAC,mBAAmB;IAClC,sBAAc,CAAC,mBAAmB;IAClC,aAAa;IACb,sBAAc,CAAC,mBAAmB;IAClC,sBAAc,CAAC,eAAe;IAC9B,iBAAiB;IACjB,sBAAc,CAAC,UAAU;IACzB,sBAAc,CAAC,UAAU;IACzB,sBAAc,CAAC,WAAW;IAC1B,sBAAc,CAAC,gBAAgB;IAC/B,sBAAc,CAAC,aAAa;IAC5B,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,eAAe;IAC9B,sBAAc,CAAC,0BAA0B;IACzC,sBAAc,CAAC,4BAA4B;IAC3C,sBAAc,CAAC,eAAe;IAC9B,sBAAc,CAAC,WAAW;IAC1B,sBAAc,CAAC,SAAS;CACzB,CAAC,CAAC;AAEH,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,QAAQ;IACd,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,sBAAsB,CAAC;QACpC,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,gCAAgC;YAC7C,oCAAoC;YACpC,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE;QACd,oDAAoD;QACpD,CAAC;QACD;YACE,kDAAkD;YAClD,2FAA2F;YAC3F,UAAU,EAAE,CAAC;YACb,sBAAsB,EAAE,KAAK;YAC7B,YAAY,EAAE,EAAE;SACjB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,mBAAmB;QACjC,sEAAsE;QACtE,oDAAoD;QACpD,MAAM,mBAAmB,GAAmB,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACjE,OAAO,EAAE;gBACP,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,KAAK;gBACnB,KAAK,EAAE,mBAAmB;aAC3B;SACF,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAEnD;;;;;WAKG;QACH,SAAS,6BAA6B,CACpC,IAGwB,EACxB,OAEwC,sBAAc,CAAC,QAAQ;YAE/D,MAAM,IAAI,GAAG;gBACX,oCAAoC;gBACpC,GAAG,EAAE,IAAW;gBAChB,KAAK,EAAE,IAAW;gBAElB,iBAAiB;gBACjB,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,MAAM;gBACZ,4DAA4D;gBAC5D,SAAS,EAAE,IAAI;gBAEf,gBAAgB;gBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC;YACF,IAAI,IAAI,KAAK,sBAAc,CAAC,QAAQ,EAAE,CAAC;gBACrC,OAAO;oBACL,IAAI;oBACJ,GAAG,IAAI;iBACa,CAAC;YACzB,CAAC;YACD,OAAO;gBACL,IAAI;gBACJ,aAAa,EAAE,SAAS;gBACxB,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,KAAK;gBACb,cAAc,EAAE,SAAS;gBACzB,GAAG,IAAI;aACuB,CAAC;QACnC,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE;YAC9B,0EAA0E;YAC1E,QAAQ,CAAC,IAAmB;gBAC1B,mGAAmG;gBACnG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;YAED,mBAAmB,CAAC,IAAkC;gBACpD,oEAAoE;gBACpE,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,OAAO;gBACT,CAAC;gBAED,OAAO,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACzC,CAAC;YAED,cAAc,CAAC,IAA6B;gBAC1C,qCAAqC;gBACrC,OAAO,KAAK,CAAC,qCAAqC,CAAC,CAAC;oBAClD,IAAI,EAAE,sBAAc,CAAC,gBAAgB;oBACrC,QAAQ,EAAE,IAAW;oBACrB,IAAI,EAAE,IAAI,CAAC,UAAU;oBACrB,iDAAiD;oBACjD,KAAK,EAAE,IAAI,CAAC,cAAqB;oBAEjC,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,iBAAiB,CAAC,IAAgC;gBAChD,0CAA0C;gBAC1C,OAAO,KAAK,CAAC,qBAAqB,CAAC;oBACjC,IAAI,EAAE,sBAAc,CAAC,qBAAqB;oBAC1C,IAAI,EAAE;wBACJ,MAAM,EAAE,IAAI;wBACZ,IAAI,EAAE,sBAAc,CAAC,gBAAgB;wBACrC,QAAQ,EAAE,SAAgB;wBAC1B,IAAI,EAAE,IAAI,CAAC,SAAgB;wBAC3B,KAAK,EAAE,IAAI,CAAC,WAAkB;wBAE9B,gBAAgB;wBAChB,KAAK,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC3D,GAAG,EAAE;4BACH,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK;4BAC/B,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG;yBAC9B;qBACF;oBACD,UAAU,EAAE,IAAI,CAAC,QAAe;oBAChC,SAAS,EAAE,IAAI,CAAC,SAAgB;oBAEhC,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,kCAAkC,CAChC,IAAyD;gBAEzD,sCAAsC;gBACtC,OAAO,KAAK,CAAC,iCAAiC,CAAC,CAAC;oBAC9C,IAAI,EAAE,sBAAc,CAAC,gBAAgB;oBACrC,UAAU,EACR,IAAI,CAAC,OACN,CAAC,GAAG,CACH,MAAM,CAAC,EAAE,CACP,6BAA6B,CAAC,MAAM,CAAsB,CAC7D;oBAED,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,yBAAyB,CAAC,IAAwC;gBAChE,yCAAyC;gBACzC,+FAA+F;gBAC/F,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;gBAErC,OAAO,KAAK,CAAC,mBAAmB,CAAC;oBAC/B,IAAI,EAAE,sBAAc,CAAC,mBAAmB;oBACxC,IAAI,EAAE,OAAgB;oBACtB,YAAY,EAAE;wBACZ;4BACE,IAAI,EAAE,sBAAc,CAAC,kBAAkB;4BACvC,KAAK,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;4BAC9C,GAAG,EAAE;gCACH,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK;gCACnB,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG;6BAC7B;4BACD,EAAE,EAAE,EAAE;4BACN,IAAI,EAAE;gCACJ,IAAI,EAAE,sBAAc,CAAC,cAAc;gCACnC,MAAM,EAAE;oCACN,IAAI,EAAE,sBAAc,CAAC,UAAU;oCAC/B,IAAI,EAAE,SAAS;oCACf,KAAK,EAAE;wCACL,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;wCACxB,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;qCAC5C;oCACD,GAAG,EAAE;wCACH,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK;wCAChC,GAAG,EAAE;4CACH,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;4CAClC,MAAM,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM;yCAC1D;qCACF;iCACF;gCACD,SAAS,EACP,YAAY,IAAI,eAAe;oCAC7B,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC;oCAC9B,CAAC,CAAC,EAAE;gCAER,gBAAgB;gCAChB,KAAK,EAAE,eAAe,CAAC,KAAK;gCAC5B,GAAG,EAAE,eAAe,CAAC,GAAG;6BACzB;yBAC6B;qBACjC;oBACD,OAAO,EAAE,KAAK;oBAEd,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,mBAAmB,CAAC,IAAkC;gBACpD,gCAAgC;gBAChC,OAAO,KAAK,CAAC,qDAAqD,CAAC,CAAC;oBAClE,IAAI,EAAE,sBAAc,CAAC,gBAAgB;oBACrC,MAAM,EAAE,IAAI,CAAC,UAAiB;oBAC9B,QAAQ,EAAE,IAAI,CAAC,SAAgB;oBAE/B,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,QAAQ,EAAE,KAAK;oBACf,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;YACL,CAAC;YAED,eAAe,CAAC,IAA8B;gBAC5C,+BAA+B;gBAC/B,OAAO,KAAK,CAAC,2BAA2B,CAAC,CAAC;oBACxC,IAAI,EAAE,sBAAc,CAAC,SAAS;oBAC9B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CACjB,CAAC,CAAC,EAAE,CACF,6BAA6B,CAC3B,CAAC,EACD,sBAAc,CAAC,kBAAkB,CACH,CACnC;oBAED,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,4CAA4C,CAC1C,IAAqC;gBAErC,qCAAqC;gBACrC,OAAO,KAAK,CACV,2DAA2D,CAC5D,CAAC;oBACA,IAAI,EAAE,sBAAc,CAAC,gBAAgB;oBACrC,IAAI,EAAE,IAAI,CAAC,IAAW;oBACtB,EAAE,EAAE,IAAI;oBACR,yEAAyE;oBACzE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAiB;oBAC7C,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,EAAE;oBACd,UAAU,EAAE,EAAE;oBACd,kBAAkB,EAAE,SAAS;oBAC7B,mBAAmB,EAAE,SAAS;oBAC9B,cAAc,EAAE,SAAS;oBAEzB,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,YAAY,CAAC,IAA2B;gBACtC,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM,kBAAkB,GAAG,UAAU,CAAC,cAAc,CAClD,IAAI,CAAC,aAAa,CAClB,CAAC;gBAEH,sCAAsC;gBACtC,OAAO,KAAK,CAAC,iCAAiC,CAAC,CAAC;oBAC9C,IAAI,EAAE,sBAAc,CAAC,gBAAgB;oBACrC,UAAU,EAAE;wBACV;4BACE,MAAM,EAAE,IAAI;4BACZ,IAAI,EAAE,sBAAc,CAAC,QAAQ;4BAC7B,GAAG,EAAE,IAAI,CAAC,aAAoB;4BAC9B,KAAK,EAAE,IAAI,CAAC,cAAqB;4BAEjC,gBAAgB;4BAChB,KAAK,EAAE;gCACL,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;gCAC3B,IAAI,CAAC,cAAc;oCACjB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;oCAC9B,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;6BAChC;4BACD,GAAG,EAAE;gCACH,KAAK,EAAE,kBAAkB,CAAC,GAAG,CAAC,KAAK;gCACnC,GAAG,EAAE,IAAI,CAAC,cAAc;oCACtB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG;oCAC7B,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG;6BAC/B;4BACD,IAAI,EAAE,MAAe;4BACrB,QAAQ,EAAE,KAAK;4BACf,MAAM,EAAE,KAAK;4BACb,QAAQ,EAAE,KAAK;4BACf,SAAS,EAAE,KAAK;yBACjB;qBACF;oBAED,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,aAAa,CAAC,IAA4B;gBACxC,mCAAmC;gBACnC,OAAO,KAAK,CAAC,2BAA2B,CAAC,CAAC;oBACxC,IAAI,EAAE,sBAAc,CAAC,cAAc;oBACnC,IAAI,EAAE,IAAI,CAAC,IAAW;oBAEtB,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,eAAe,CAAC,IAA8B;gBAC5C,OAAO,KAAK,CAAC,qDAAqD,CAAC,CAAC;oBAClE,IAAI,EAAE,sBAAc,CAAC,gBAAgB;oBACrC,MAAM,EAAE,IAAI,CAAC,IAAW;oBACxB,QAAQ,EAAE,IAAI,CAAC,KAAY;oBAE3B,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,QAAQ,EAAE,KAAK;oBACf,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;YACL,CAAC;YAED,WAAW,CAAC,IAA0B;gBACpC,qCAAqC;gBACrC,OAAO,KAAK,CAAC,+BAA+B,CAAC,CAAC;oBAC5C,IAAI,EAAE,sBAAc,CAAC,eAAe;oBACpC,QAAQ,EAAE,IAAI,CAAC,YAAmB;oBAElC,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;YAED,0BAA0B,CAAC,IAAyC;gBAClE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACxB,OAAO;gBACT,CAAC;gBAED,MAAM,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBAE1C,iEAAiE;gBACjE,iCAAiC;gBACjC,OAAO,KAAK,CAAC,iBAAiB,CAAC;oBAC7B,IAAI,EAAE,sBAAc,CAAC,iBAAiB;oBACtC,WAAW,EAAE,KAAK;oBAClB,IAAI,EAAE,IAAW;oBACjB,UAAU,EAAE,UAAiB;oBAC7B,aAAa,EAAE,SAAS;oBACxB,cAAc,EAAE,SAAS;oBAEzB,gBAAgB;oBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC"}